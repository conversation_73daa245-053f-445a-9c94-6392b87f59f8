

#include "zexuan/net/event_loop_thread.hpp"
#include "zexuan/net/tcp_client.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/base/noncopyable.hpp"
#include "zexuan/base/message.hpp"
#include <iostream>
#include <stdio.h>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <signal.h>
#include <spdlog/spdlog.h>

using namespace zexuan;
using namespace zexuan::net;
using namespace zexuan::platform::network;

class ChatClient : zexuan::base::noncopyable
{
 public:
  ChatClient(EventLoop* loop, const Address& serverAddr)
    : client_(loop, serverAddr, "ChatClient"), receive_buffer_()
  {
    client_.setConnectionCallback(
        std::bind(&ChatClient::onConnection, this, std::placeholders::_1));
    client_.setMessageCallback(
        std::bind(&ChatClient::onMessage, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
    client_.enableRetry();
  }

  void connect()
  {
    client_.connect();
  }

  void disconnect()
  {
    client_.disconnect();
  }

  void write(const std::string& message)
  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (connection_)
    {
      connection_->send(message);
    }
  }

 private:
  void onConnection(const TcpConnectionPtr& conn)
  {
    spdlog::info("{} -> {} is {}",
                 conn->localAddress().toIpPort(),
                 conn->peerAddress().toIpPort(),
                 conn->connected() ? "UP" : "DOWN");

    std::lock_guard<std::mutex> lock(mutex_);
    if (conn->connected())
    {
      connection_ = conn;

      // // 连接成功后发送测试数据
      // sendSingleFrameTest();  // 先发送单帧测试

      // // 延迟发送多帧测试
      // std::this_thread::sleep_for(std::chrono::milliseconds(100));
      sendMultiFrameTest();   // 再发送多帧测试
    }
    else
    {
      connection_.reset();
    }
  }

  void onMessage(const TcpConnectionPtr& conn,
                 Buffer* buf,
                 std::chrono::system_clock::time_point)
  {
    // 添加新数据到接收缓冲区
    std::vector<uint8_t> new_data(buf->readableBytes());
    std::copy(buf->peek(), buf->peek() + buf->readableBytes(), new_data.begin());
    buf->retrieveAll();

    receive_buffer_.insert(receive_buffer_.end(), new_data.begin(), new_data.end());

    printf("<<< Received %zu bytes, buffer total: %zu bytes\n", new_data.size(), receive_buffer_.size());

    // 处理分帧
    while (true) {
      auto complete_frame = extractCompleteFrame();
      if (complete_frame.empty()) {
        break; // 没有完整帧
      }

      processCompleteFrame(complete_frame);
    }
  }

  void sendSingleFrameTest()
  {
    if (!connection_) {
      return;
    }

    printf(">>> Sending Type 1 (Single Frame) Test\n");

    // 创建 Type 1 单帧测试消息
    zexuan::base::Message msg;
    msg.setTyp(0x01);      // Type 1 = 单帧测试
    msg.setVsq(0x81);      // 可变结构限定词（单信息）
    msg.setCot(0x06);      // 传送原因（激活）
    msg.setSource(0x01);   // 源地址（客户端）
    msg.setTarget(0x02);   // 目标地址（服务器）
    msg.setFun(0xFF);      // 功能类型
    msg.setInf(0x13);      // 信息序号

    // 设置测试数据到可变结构体
    msg.setTextContent("SINGLE_FRAME_TEST_DATA");

    sendMessage(msg, "Type 1 Single Frame");
  }

  void sendMultiFrameTest()
  {
    if (!connection_) {
      return;
    }

    printf(">>> Sending Type 2 (Multi Frame) Test\n");

    // 创建 Type 2 多帧测试消息
    zexuan::base::Message msg;
    msg.setTyp(0x02);      // Type 2 = 多帧测试
    msg.setVsq(0x82);      // 可变结构限定词（多信息）
    msg.setCot(0x06);      // 传送原因（激活）
    msg.setSource(0x01);   // 源地址（客户端）
    msg.setTarget(0x02);   // 目标地址（服务器）
    msg.setFun(0xFF);      // 功能类型
    msg.setInf(0x13);      // 信息序号

    // 设置测试数据到可变结构体
    msg.setTextContent("MULTI_FRAME_TEST_DATA");

    sendMessage(msg, "Type 2 Multi Frame");
  }

  void sendMessage(const zexuan::base::Message& msg, const std::string& description)
  {
    // 序列化消息
    std::vector<uint8_t> serializedData;
    size_t size = msg.serialize(serializedData);

    if (size > 0) {
      std::string dataStr(serializedData.begin(), serializedData.end());
      connection_->send(dataStr);

      printf(">>> Sent %s (%zu bytes): ", description.c_str(), size);
      for (uint8_t byte : serializedData) {
        printf("%02X ", byte);
      }
      printf("\n");

      spdlog::info("Sent {}: TYP={:02X}, VSQ={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                   description, msg.getTyp(), msg.getVsq(), msg.getCot(), msg.getSource(), msg.getTarget(), msg.getFun(), msg.getInf());
    } else {
      spdlog::error("Failed to serialize {}", description);
    }
  }

  std::vector<uint8_t> extractCompleteFrame()
  {
    if (receive_buffer_.size() < 3) {
      return {}; // 至少需要帧头
    }

    // 查找帧起始符 0x68
    auto start_it = std::find(receive_buffer_.begin(), receive_buffer_.end(), 0x68);
    if (start_it == receive_buffer_.end()) {
      // 没有找到起始符，清空缓冲区
      receive_buffer_.clear();
      printf("<<< No frame start found, clearing buffer\n");
      return {};
    }

    // 删除起始符之前的垃圾数据
    if (start_it != receive_buffer_.begin()) {
      size_t skip = start_it - receive_buffer_.begin();
      receive_buffer_.erase(receive_buffer_.begin(), start_it);
      printf("<<< Skipped %zu bytes of garbage data\n", skip);
    }

    if (receive_buffer_.size() < 3) {
      return {}; // 等待更多数据
    }

    // 解析帧长度
    uint16_t asdu_len = receive_buffer_[1] | (static_cast<uint16_t>(receive_buffer_[2]) << 8);
    size_t total_frame_len = 3 + asdu_len;

    // 帧长度合法性检查
    if (asdu_len > 1024) {
      receive_buffer_.erase(receive_buffer_.begin()); // 删除错误的起始符
      printf("<<< Invalid frame length %u, skipping\n", asdu_len);
      return {};
    }

    // 检查是否有完整帧
    if (receive_buffer_.size() < total_frame_len) {
      printf("<<< Incomplete frame, need %zu bytes, have %zu bytes\n", total_frame_len, receive_buffer_.size());
      return {}; // 等待更多数据
    }

    // 提取完整帧
    std::vector<uint8_t> complete_frame(receive_buffer_.begin(), receive_buffer_.begin() + total_frame_len);
    receive_buffer_.erase(receive_buffer_.begin(), receive_buffer_.begin() + total_frame_len);

    printf("<<< Extracted complete frame of %zu bytes\n", total_frame_len);
    return complete_frame;
  }

  void processCompleteFrame(const std::vector<uint8_t>& frame_data)
  {
    printf("<<< Processing complete frame: ");
    for (uint8_t byte : frame_data) {
      printf("%02X ", byte);
    }
    printf("\n");

    // 尝试解析为 IEC103 消息
    zexuan::base::Message receivedMsg;
    size_t parsed = receivedMsg.deserialize(frame_data);

    if (parsed > 0) {
      printf("<<< Parsed IEC103 message: TYP=%02X, VSQ=%02X, COT=%02X, SRC=%02X, TGT=%02X, FUN=%02X, INF=%02X\n",
             receivedMsg.getTyp(), receivedMsg.getVsq(), receivedMsg.getCot(),
             receivedMsg.getSource(), receivedMsg.getTarget(), receivedMsg.getFun(), receivedMsg.getInf());

      std::string content = receivedMsg.getTextContent();
      if (!content.empty()) {
        printf("<<< Message content: %s\n", content.c_str());
      }

      // 根据消息类型判断是否为多帧响应
      if (receivedMsg.getTyp() == 2) {
        printf("<<< This is a Type 2 multi-frame response\n");
      } else {
        printf("<<< This is a Type 1 single-frame response\n");
      }
    } else {
      // 也显示为字符串（如果不能解析为IEC103）
      std::string msg_str(frame_data.begin(), frame_data.end());
      printf("<<< As string: %s\n", msg_str.c_str());
    }
  }

 private:
  TcpClient client_;
  std::mutex mutex_;
  TcpConnectionPtr connection_;
  std::vector<uint8_t> receive_buffer_;  // 接收缓冲区，用于分帧处理
};

int main(int argc, char* argv[])
{
  spdlog::info("pid = {}", getpid());
  
  if (argc > 2)
  {
    EventLoopThread loopThread;
    EventLoop* loop = loopThread.startLoop();
    
    uint16_t port = static_cast<uint16_t>(atoi(argv[2]));
    Address serverAddr(argv[1], port);

    ChatClient client(loop, serverAddr);
    client.connect();
    spdlog::info("Protocol test client connected, will send 103 test data automatically");

    // 等待一段时间让数据传输完成，然后可以手动输入更多数据
    spdlog::info("Waiting for response... You can also input additional messages:");

    std::string line;
    while (std::getline(std::cin, line))
    {
      if (line == "quit" || line == "exit") {
        break;
      }
      client.write(line);
    }
    client.disconnect();
    std::this_thread::sleep_for(std::chrono::seconds(1));  // wait for disconnect
  }
  else
  {
    printf("Usage: %s host_ip port\n", argv[0]);
  }
}
